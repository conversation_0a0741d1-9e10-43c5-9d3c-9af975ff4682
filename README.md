# Hospital Management System

A comprehensive Java-based Hospital Management System with MySQL database integration.

## Prerequisites

1. **Java Development Kit (JDK) 11 or higher**
2. **MySQL Server** (8.0 or higher recommended)
3. **Maven** (for dependency management)
4. **MySQL JDBC Driver** (already included in pom.xml)

## Setup Instructions

### 1. Database Setup

1. **Install MySQL Server** if not already installed
2. **Start MySQL service**
3. **Create the database** by running the SQL script:
   ```sql
   mysql -u root -p < database/hospital_schema.sql
   ```
   Or manually execute the SQL commands in `database/hospital_schema.sql`

### 2. Configure Database Connection

1. Open `src/main/java/com/hospital/config/DatabaseConnection.java`
2. Update the database configuration:
   ```java
   private static final String DB_URL = "***********************************************";
   private static final String DB_USERNAME = "root";  // Your MySQL username
   private static final String DB_PASSWORD = "your_password_here";  // Your MySQL password
   ```

### 3. MySQL JDBC Driver Setup

The MySQL JDBC driver is automatically managed by <PERSON><PERSON>. If you downloaded the driver manually:

#### Option A: Using Maven (Recommended)
The `pom.xml` already includes the MySQL connector dependency:
```xml
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.33</version>
</dependency>
```

#### Option B: Manual JAR file
If you have a JAR file downloaded from Chrome:
1. Create a `lib` folder in your project root
2. Copy the `mysql-connector-java-x.x.x.jar` file to the `lib` folder
3. Add it to your classpath when compiling/running

### 4. Build and Run

1. **Install dependencies:**
   ```bash
   mvn clean install
   ```

2. **Run the application:**
   ```bash
   mvn exec:java
   ```

   Or compile and run manually:
   ```bash
   mvn compile
   java -cp target/classes:~/.m2/repository/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar com.hospital.Main
   ```

## Project Structure

```
Hospital_management_System/
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── hospital/
│                   ├── Main.java
│                   └── config/
│                       └── DatabaseConnection.java
├── database/
│   └── hospital_schema.sql
├── pom.xml
└── README.md
```

## Features

- **Database Connection Management**: Robust MySQL connection handling
- **Patient Management**: Store and manage patient information
- **Doctor Management**: Manage doctor profiles and specializations
- **Appointment Scheduling**: Schedule and track appointments
- **Medical Records**: Maintain patient medical history
- **Department Management**: Organize hospital departments

## Troubleshooting

### Common Issues:

1. **"MySQL JDBC Driver not found"**
   - Ensure Maven dependencies are installed: `mvn clean install`
   - Check if the JAR file is in the correct location

2. **"Access denied for user"**
   - Verify MySQL username and password in `DatabaseConnection.java`
   - Ensure MySQL server is running

3. **"Unknown database 'hospital_management'"**
   - Run the SQL script to create the database: `mysql -u root -p < database/hospital_schema.sql`

4. **Connection timeout**
   - Check if MySQL server is running: `sudo service mysql start` (Linux) or start MySQL service (Windows)
   - Verify the database URL and port (default: 3306)

## Next Steps

1. Test the database connection by running the application
2. Implement additional features like:
   - Patient registration forms
   - Appointment booking system
   - Medical record management
   - Billing system
   - Reports and analytics

## Database Schema

The system includes the following main tables:
- `patients` - Patient information
- `doctors` - Doctor profiles
- `appointments` - Appointment scheduling
- `medical_records` - Patient medical history
- `departments` - Hospital departments

See `database/hospital_schema.sql` for complete schema details.
