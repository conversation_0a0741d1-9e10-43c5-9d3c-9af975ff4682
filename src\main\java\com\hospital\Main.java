package com.hospital;

import com.hospital.config.DatabaseConnection;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Main class for Hospital Management System
 */
public class Main {
    
    public static void main(String[] args) {
        System.out.println("=== Hospital Management System ===");
        System.out.println("Starting application...");
        
        // Test database connection
        testDatabaseConnection();
        
        // Initialize the application
        initializeApplication();
    }
    
    /**
     * Test database connection
     */
    private static void testDatabaseConnection() {
        System.out.println("\nTesting database connection...");
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            
            if (conn != null && !conn.isClosed()) {
                System.out.println("✓ Database connection successful!");
                
                // Get database metadata
                String dbName = conn.getCatalog();
                String dbURL = conn.getMetaData().getURL();
                String dbUser = conn.getMetaData().getUserName();
                
                System.out.println("Database Name: " + dbName);
                System.out.println("Database URL: " + dbURL);
                System.out.println("Database User: " + dbUser);
                
            } else {
                System.out.println("✗ Failed to connect to database!");
            }
            
        } catch (SQLException e) {
            System.out.println("✗ Database connection failed!");
            System.out.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Initialize the application
     */
    private static void initializeApplication() {
        System.out.println("\nInitializing Hospital Management System...");
        
        // Here you can add your application logic
        // For example: show main menu, initialize GUI, etc.
        
        System.out.println("Application ready!");
        
        // Close database connection when application ends
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\nShutting down application...");
            DatabaseConnection.closeConnection();
        }));
    }
}
